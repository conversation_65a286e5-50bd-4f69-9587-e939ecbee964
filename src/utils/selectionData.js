/**
 * Selection Data Utilities
 * 
 * Utilities for managing citizen and insurer selection data for the two-step
 * member selection process. Provides functions to extract unique citizens
 * and their associated insurers from member data.
 */

import { getAllMembers } from './memberData.js';

/**
 * Map insurer codes to display names
 */
const INSURER_DISPLAY_MAP = {
  'INS001': 'บริษัท ประกันภัย เอ จำกัด (Insurance Company A Ltd.)',
  'INS002': 'บริษัท ประกันชีวิต แอล จำกัด (Life Insurance L Ltd.)',
  'INS003': 'บริษัท ประกันชีวิต ซี จำกัด (Life Insurance C Ltd.)'
};

/**
 * Map citizen IDs to display information
 */
const CITIZEN_DISPLAY_MAP = {
  '1234567890123': {
    titleTH: 'นาย',
    nameTH: 'สมชาย',
    surnameTH: 'ใจดี',
    titleEN: 'Mr.',
    nameEN: 'Somcha<PERSON>',
    surnameEN: '<PERSON><PERSON><PERSON>'
  },
  '1234567890124': {
    titleTH: 'นาง',
    nameTH: 'มาลี',
    surnameTH: 'สวยงาม',
    titleEN: 'Mrs.',
    nameEN: '<PERSON><PERSON>',
    surnameEN: '<PERSON><PERSON><PERSON><PERSON>'
  },
  '1234567890125': {
    titleTH: 'นางสาว',
    nameTH: 'ปิยะดา',
    surnameTH: 'สุขใส',
    titleEN: 'Ms.',
    nameEN: '<PERSON><PERSON><PERSON>',
    surnameEN: '<PERSON><PERSON><PERSON>'
  }
};

/**
 * Get display name for insurer code
 * @param {string} insurerCode - Insurer code (e.g., 'INS001')
 * @returns {string} Display name for insurer
 */
export function getInsurerDisplayName(insurerCode) {
  return INSURER_DISPLAY_MAP[insurerCode] || insurerCode || 'Unknown Insurer';
}

/**
 * Get display name for citizen ID
 * @param {string} citizenID - Citizen ID (e.g., 'CIT001')
 * @param {string} language - Language preference ('TH' or 'EN')
 * @returns {string} Display name for citizen
 */
export function getCitizenDisplayName(citizenID, language = 'TH') {
  const citizen = CITIZEN_DISPLAY_MAP[citizenID];
  if (!citizen) return citizenID || 'Unknown Citizen';

  if (language === 'EN') {
    return `${citizen.titleEN} ${citizen.nameEN} ${citizen.surnameEN}`;
  }
  return `${citizen.titleTH} ${citizen.nameTH} ${citizen.surnameTH}`;
}

/**
 * Get all unique citizens from member data
 * @returns {Array} Array of citizen objects with display information
 */
export function getAllCitizens() {
  const members = getAllMembers();
  const citizenMap = new Map();

  // Extract unique citizens
  members.forEach(member => {
    if (!citizenMap.has(member.citizenID)) {
      citizenMap.set(member.citizenID, {
        citizenID: member.citizenID,
        displayName: getCitizenDisplayName(member.citizenID, 'TH'),
        displayNameEN: getCitizenDisplayName(member.citizenID, 'EN'),
        titleTH: member.titleTH,
        nameTH: member.nameTH,
        surnameTH: member.surnameTH,
        titleEN: member.titleEN,
        nameEN: member.nameEN,
        surnameEN: member.surnameEN
      });
    }
  });

  return Array.from(citizenMap.values()).sort((a, b) =>
    a.displayName.localeCompare(b.displayName, 'th')
  );
}

/**
 * Get insurers available for a specific citizen
 * @param {string} citizenID - Citizen ID to get insurers for
 * @returns {Array} Array of insurer objects for the citizen
 */
export function getInsurersForCitizen(citizenID) {
  if (!citizenID) return [];

  const members = getAllMembers();
  const insurerMap = new Map();

  // Find all insurers for this citizen
  members
    .filter(member => member.citizenID === citizenID)
    .forEach(member => {
      if (!insurerMap.has(member.insurerCode)) {
        insurerMap.set(member.insurerCode, {
          insurerCode: member.insurerCode,
          displayName: getInsurerDisplayName(member.insurerCode),
          memberCode: member.memberCode // Store associated member code
        });
      }
    });

  return Array.from(insurerMap.values()).sort((a, b) =>
    a.displayName.localeCompare(b.displayName, 'th')
  );
}

/**
 * Get all available insurers
 * @returns {Array} Array of all insurer objects
 */
export function getAllInsurers() {
  const members = getAllMembers();
  const insurerMap = new Map();

  members.forEach(member => {
    if (!insurerMap.has(member.insurerCode)) {
      insurerMap.set(member.insurerCode, {
        insurerCode: member.insurerCode,
        displayName: getInsurerDisplayName(member.insurerCode)
      });
    }
  });

  return Array.from(insurerMap.values()).sort((a, b) =>
    a.displayName.localeCompare(b.displayName, 'th')
  );
}

/**
 * Find member by citizen ID and insurer code
 * @param {string} citizenID - Citizen ID
 * @param {string} insurerCode - Insurer code
 * @returns {Object|null} Member object or null if not found
 */
export function findMemberByCitizenAndInsurer(citizenID, insurerCode) {
  if (!citizenID || !insurerCode) return null;

  const members = getAllMembers();
  return members.find(member =>
    member.citizenID === citizenID && member.insurerCode === insurerCode
  ) || null;
}

/**
 * Validate citizen and insurer combination
 * @param {string} citizenID - Citizen ID to validate
 * @param {string} insurerCode - Insurer code to validate
 * @returns {Object} Validation result with success flag and message
 */
export function validateCitizenInsurerCombination(citizenID, insurerCode) {
  if (!citizenID) {
    return { success: false, message: 'กรุณาเลือกสมาชิก' };
  }

  if (!insurerCode) {
    return { success: false, message: 'กรุณาเลือกบริษัทประกัน' };
  }

  const member = findMemberByCitizenAndInsurer(citizenID, insurerCode);
  if (!member) {
    return {
      success: false,
      message: 'ไม่พบข้อมูลสมาชิกสำหรับการเลือกนี้'
    };
  }

  return {
    success: true,
    message: 'การเลือกถูกต้อง',
    member
  };
}

/**
 * Get citizen options for dropdown display
 * @returns {Array} Array of citizen options formatted for dropdown
 */
export function getCitizenOptions() {
  const citizens = getAllCitizens();
  return citizens.map(citizen => ({
    value: citizen.citizenID,
    label: citizen.displayName,
    labelEN: citizen.displayNameEN,
    citizen: citizen
  }));
}

/**
 * Get insurer options for dropdown display
 * @param {string} citizenID - Citizen ID to get insurers for (optional)
 * @returns {Array} Array of insurer options formatted for dropdown
 */
export function getInsurerOptions(citizenID = null) {
  const insurers = citizenID ? getInsurersForCitizen(citizenID) : getAllInsurers();
  return insurers.map(insurer => ({
    value: insurer.insurerCode,
    label: insurer.displayName,
    insurer: insurer
  }));
}
